<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.haoxueren.pray.manage.SettingsActivity"
    tools:ignore="HardcodedText">

    <androidx.appcompat.widget.Toolbar
        style="@style/ToolbarTheme"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/theme"
        app:subtitle="设置"
        app:subtitleTextColor="@color/white" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/fingerprintSwitch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_default"
        android:text="启用指纹验证"
        android:textStyle="bold" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/keyboardFeature"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_default"
        android:text="开启输入框悬浮"
        android:textStyle="bold" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/modeSwitch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_underline"
        android:padding="@dimen/padding_default"
        android:text="使用API请求数据"
        android:textStyle="bold" />


    <Button
        android:id="@+id/backupButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_default"
        android:text="备份数据库"
        android:textAllCaps="false" />






</LinearLayout>
