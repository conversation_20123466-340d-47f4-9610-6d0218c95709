<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.haoxueren.pray"
    tools:ignore="LockedOrientationActivity,GoogleAppIndexingWarning">
    <!-- 允许联网 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 获取GSM（2g）、WCDMA（联通3g）等网络状态的信息 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 获取wifi网络状态的信息 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 指纹验证 -->
    <uses-permission android:name="android.permission.CHANGE_BADGE" /> <!-- 桌面图标小红点 -->
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <application
        android:name=".MyApplication"
        android:icon="@mipmap/icon_pray_256x256"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/icon_pray_256x256"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".main.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name=".group.AllGroupActivity" />
        <activity android:name=".group.PrayRecordDetailActivity" />
        <activity android:name=".manage.StatisticsActivity" />
        <activity android:name=".manage.DatabaseManageActivity" />
        <activity android:name=".manage.SettingsActivity" />
        <activity android:name=".sync.DatabaseSyncActivity" />
        <activity android:name=".debug.NetworkDiagnosticActivity" />
    </application>

</manifest>