package com.haoxueren.pray.main

import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import com.haoxueren.pray.R

@Composable
fun MainToolbar(
    subtitle: String = "我是标题",
    onNavigationClick: () -> Unit,
    onRefreshClick: () -> Unit,
    onPrayClick: () -> Unit,
    onAllGroupClick: () -> Unit,
    onStatisticsClick: () -> Unit,
    onDatabaseManageClick: () -> Unit,
    onDatabaseSyncClick: () -> Unit,
    onSettingsClick: () -> Unit
) {
    var showMenu by remember { mutableStateOf(false) }

    TopAppBar(
        title = { 
            Text(
                text = subtitle,
                fontSize = 15.sp,
                modifier = Modifier.offset(x = (-24).dp)
            )
        },
        navigationIcon = {
            IconButton(onClick = onNavigationClick) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回"
                )
            }
        },
        backgroundColor = colorResource(id = R.color.theme),
        contentColor = Color.White,
        actions = {
            Row {
                // 刷新按钮
                IconButton(
                    onClick = onRefreshClick,
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Refresh,
                        contentDescription = "刷新"
                    )
                }
                
                // 保存按钮
                IconButton(
                    onClick = onPrayClick,
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Done,
                        contentDescription = "保存"
                    )
                }
                
                // 更多菜单
                IconButton(
                    onClick = { showMenu = true },
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.MoreVert,
                        contentDescription = "更多"
                    )
                }
            }
            
            DropdownMenu(
                expanded = showMenu,
                onDismissRequest = { showMenu = false }
            ) {
                DropdownMenuItem(
                    onClick = {
                        showMenu = false
                        onAllGroupClick()
                    }
                ) {
                    Text("分组")
                }
                
                DropdownMenuItem(
                    onClick = {
                        showMenu = false
                        onStatisticsClick()
                    }
                ) {
                    Text("统计")
                }

                DropdownMenuItem(
                    onClick = {
                        showMenu = false
                        onDatabaseManageClick()
                    }
                ) {
                    Text("数据库管理")
                }

                DropdownMenuItem(
                    onClick = {
                        showMenu = false
                        onDatabaseSyncClick()
                    }
                ) {
                    Text("数据库同步")
                }

                DropdownMenuItem(
                    onClick = {
                        showMenu = false
                        onSettingsClick()
                    }
                ) {
                    Text("设置")
                }
            }
        }
    )
} 