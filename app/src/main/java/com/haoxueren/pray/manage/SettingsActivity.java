package com.haoxueren.pray.manage;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;

import androidx.appcompat.widget.SwitchCompat;

import com.haoxueren.base.BaseActivity;
import com.haoxueren.pray.R;
import com.haoxueren.sqlite.BackupHelper;
import com.haoxueren.sqlite.SocketManager;
import com.haoxueren.sqlite.EnhancedSocketManager;
import com.haoxueren.utils.ErrorUtils;
import com.haoxueren.utils.MyPreferences;
import com.haoxueren.utils.ToastUtils;

/**
 * 设置界面
 */
@SuppressLint("CheckResult")
public class SettingsActivity extends BaseActivity {

    private SwitchCompat keyboardSwitch, modeSwitch;
    private SwitchCompat fingerprintSwitch;
    private Button backupButton;

    public static void start(Context context) {
        Intent intent = new Intent(context, SettingsActivity.class);
        context.startActivity(intent);
    }


    @Override
    public int getLayoutResId() {
        return R.layout.activity_settings;
    }

    @Override
    protected void bindView(View layout) {
        modeSwitch = this.findViewById(R.id.modeSwitch);
        keyboardSwitch = this.findViewById(R.id.keyboardFeature);
        fingerprintSwitch = this.findViewById(R.id.fingerprintSwitch);
        backupButton = this.findViewById(R.id.backupButton);
    }

    @Override
    protected void initView() {
        MyPreferences preferences = MyPreferences.getInstance();
        modeSwitch.setChecked(preferences.getApiMode());
        keyboardSwitch.setChecked(preferences.getKeyboardFlag());
        fingerprintSwitch.setChecked(preferences.isFingerprintEnabled());
        
        modeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            preferences.setApiMode(isChecked);
        });
        keyboardSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            preferences.setKeyboardFlag(isChecked);
        });
        fingerprintSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            preferences.setFingerprintEnabled(isChecked);
            ToastUtils.showToast(isChecked ? "指纹验证已启用" : "指纹验证已禁用");
        });
        backupButton.setOnClickListener(v -> {
            backupButton.setEnabled(false);
            BackupHelper.backupSQLite(this)
                    .subscribe(file -> backupButton.setText(file.getPath()), ErrorUtils::onError);
        });
    }

    @Override
    protected void initData() {
        SocketManager.startServer(2024);
        // 启动增强的Socket服务器
        EnhancedSocketManager.startEnhancedServer(2024);
    }

    @Override
    protected void onStart() {
        super.onStart();
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }

    @Override
    protected void onStop() {
        super.onStop();
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        SocketManager.running = false;
    }






}
